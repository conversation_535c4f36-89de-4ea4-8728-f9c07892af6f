
import React from 'react';

export const BriefcaseIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 14.15v4.073a2.25 2.25 0 01-2.25 2.25h-12a2.25 2.25 0 01-2.25-2.25V14.15M18.75 18.75v-6.172a2.25 2.25 0 00-2.25-2.25h-6a2.25 2.25 0 00-2.25 2.25v6.172M15 12V6.375A3.375 3.375 0 0011.625 3H6.375A3.375 3.375 0 003 6.375V12m18 0v-2.625c0-.621-.504-1.125-1.125-1.125H19.5M12 4.5h.008v.008H12V4.5z" />
  </svg>
);
